-- Migration number: 0005 	 2025-07-26T22:24:09.891Z
DROP VIEW IF EXISTS view_apps_by_week;

CREATE view view_apps_by_week AS SELECT 
  DATE(created_at, 'weekday 0', CASE strftime('%w', created_at) WHEN '0' THEN '0 days' ELSE '-7 days' END) AS week,
  COUNT(*) AS total_count,
  SUM(CASE WHEN status != 'PREQUAL_DENIED' THEN 1 ELSE 0 END) AS approved_count,
  SUM(CASE WHEN status = 'PREQUAL_DENIED' THEN 1 ELSE 0 END) AS denied_count
FROM applications
GROUP BY week
ORDER BY week DESC;
