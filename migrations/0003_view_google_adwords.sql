-- Migration number: 0003 	 2025-07-20T09:57:36.200Z
DROP VIEW IF EXISTS view_google_adwords;

CREATE VIEW view_google_adwords AS
WITH RECURSIVE last_30_days(date) AS (
  SELECT DATE('now', '-4 hours')
  UNION ALL
  SELECT DATE(date, '-1 day')
  FROM last_30_days
  WHERE date > DATE('now', '-4 hours', '-29 days')
),
counts AS (
  SELECT 
    DATE(datetime(created_at, '-4 hours')) AS date,
    COUNT(CASE WHEN status != 'PREQUAL_DENIED' THEN 1 END) AS approved,
    COUNT(CASE WHEN status = 'PREQUAL_DENIED' THEN 1 END) AS denied
  FROM applications
  WHERE json_extract(utm, '$.utm_source') = 'GoogleAdwords'
  GROUP BY DATE(datetime(created_at, '-4 hours'))
),
joined AS (
  SELECT 
    d.date,
    COALESCE(c.approved, 0) AS approved,
    COALESCE(c.denied, 0) AS denied
  FROM last_30_days d
  LEFT JOIN counts c ON d.date = c.date
)
SELECT 
  date,
  approved,
  denied,
  CAST(
    100.0 * approved / NULLIF(approved + denied, 0)
    AS INTEGER
  ) || '%' AS approved_percent
FROM joined
ORDER BY date DESC;


DROP VIEW IF EXISTS view_utm_term;

CREATE VIEW view_utm_term AS
SELECT 
  json_extract(utm, '$.utm_term') AS utm_term,
  COUNT(*) AS total_count,
  SUM(CASE WHEN status != 'PREQUAL_DENIED' THEN 1 ELSE 0 END) AS approved_count,
  SUM(CASE WHEN status = 'PREQUAL_DENIED' THEN 1 ELSE 0 END) AS denied_count
FROM applications
WHERE json_extract(utm, '$.utm_term') IS NOT NULL
GROUP BY utm_term
ORDER BY total_count DESC;
