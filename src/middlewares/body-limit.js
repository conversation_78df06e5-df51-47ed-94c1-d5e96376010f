import { bodyLimit } from 'hono/body-limit';
import { createMiddleware } from 'hono/factory';
import { AppError } from '../utils/helpers';

const MAX_BODY_SIZE_BYTES = 1 * 1024 * 1024; // 1 MB
const FILE_BODY_SIZE_BYTES = 13 * 1024 * 1024; // 13 MB (6 files * 2MB + 1MB other data)

export const apiBodyLimitMiddleware = createMiddleware(async (c, next) => {
  const path = c.req.path;
  const isFileUploadEndpoint = path.match(/^\/app\/[^/]+\/(complete|upload)$/);

  await bodyLimit({
    maxSize: isFileUploadEndpoint ? FILE_BODY_SIZE_BYTES : MAX_BODY_SIZE_BYTES,
    onError: (c) => {
      throw new AppError('Payload too large', 413, 'bodyLimitMiddleware');
    },
  })(c, next);
});

// export const fileBodyLimitMiddleware = bodyLimit(10 * 1024 * 1024); // 10 MB
