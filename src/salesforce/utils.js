export class SalesforceUtils {
  static getDealStage(status) {
    const statusToStage = {
      PREQUAL_APPROVED: 'Interested',
      PREQUAL_FAST_TRACK: 'Interested',
      PREQUAL_DENIED: 'Unqualified',
      APP_STARTED: 'Interested',
      APP_SUBMITTED: 'App Out',
      APP_EDITING: 'App Out',
      APP_SIGNED: 'Waiting on Statements',
      APP_DOCS_PENDING: 'Waiting on Statements',
      APP_COMPLETED: 'Application',
    };

    let stage = statusToStage[status] || null;

    console.log('[SalesforceUtils.getDealStage]', status, 'Stage:', stage);

    return stage;
  }

  static getCreditScore(estimatedFICO) {
    if (estimatedFICO === '300-550') return '<550';
    if (['700-780', '780-850'].includes(estimatedFICO)) return '700+';
    return estimatedFICO || null;
  }

  static maskFields(Deal__c) {
    const fields = ['EIN__c', 'SSN_O1__c', 'DOB_O1__c', 'SSN_O2__c', 'DOB_O2__c'];
    const mask = (v) => '*'.repeat(v.length - 2) + v.slice(-2);
    return {
      ...Deal__c,
      ...fields.reduce((acc, f) => {
        if (Deal__c.hasOwnProperty(f) && Deal__c[f] != null) acc[f] = mask(Deal__c[f]);
        return acc;
      }, {}),
    };
  }

  static printMeta(obj, path = '') {
    return Object.entries(obj).reduce((str, [key, val]) => {
      const fullKey = path ? `${path} → ${key}` : key;
      return (
        str + (val && typeof val === 'object' && !Array.isArray(val) ? this.printMeta(val, fullKey) : `<b>${fullKey}</b>: ${val}<br/>`)
      );
    }, '');
  }

  static parseMonthlyRevenue(monthlyRevenue, annualRevenue) {
    if (typeof annualRevenue === 'number' && annualRevenue >= 0) {
      return Math.round(annualRevenue / 12 / 500) * 500;
    }

    if (!monthlyRevenue) return null;

    const sanitized = monthlyRevenue.replace(/\+/, '');
    const [min, max] = sanitized.split('-').map(Number);

    if (!max) return min; // e.g. '500000+'

    return Math.round((min + max) / 2);
  }

  static getResumeUrl(env, domain, status, uuid) {
    if (!env || !uuid) throw new Error('Missing params for getResumeUrl');
    const appPortalUrl = domain ? `https://${domain}` : env.PORTAL_URL;
    const portalPath = status === 'PREQUAL_DENIED' ? 'prequalify-result' : 'application';

    let suffix = '';
    if (status === 'APP_DOCS_PENDING') {
      suffix = '/upload';
    } else if (status === 'APP_COMPLETED') {
      suffix = '/result';
    }

    return `${appPortalUrl}/${portalPath}/${uuid}${suffix}`;
  }
}
