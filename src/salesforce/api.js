const log = (...args) => console.log('[SF API]', ...args);

/**
 * Salesforce API client for Cloudflare Workers
 * Provides methods to interact with Salesforce REST API
 */

import { authenticateWithSalesforce } from './auth';

/**
 * Make a request to the Salesforce REST API
 * @param {Object} env - Environment variables
 * @param {string} method - HTTP method (GET, POST, PATCH, DELETE)
 * @param {string} path - API endpoint path (e.g., '/services/data/v58.0/sobjects/Account')
 * @param {Object} [data] - Request body for POST/PATCH requests
 * @param {Object} [params] - Query parameters
 * @returns {Promise<Object>} - API response
 */
async function salesforceApiRequest(env, method, path, data = null, params = null) {
  // Get access token, will refresh if needed
  const { accessToken, instanceUrl } = await authenticateWithSalesforce(env);

  // Build URL with query parameters if provided
  let url = `${instanceUrl}${path}`;
  if (params) {
    const queryString = new URLSearchParams(params).toString();
    url = `${url}${url.includes('?') ? '&' : '?'}${queryString}`;
  }

  // Prepare request options
  const options = {
    method,
    headers: {
      Authorization: `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
  };

  // Add request body for POST/PATCH requests
  if (data && (method === 'POST' || method === 'PATCH' || method === 'PUT')) {
    options.body = JSON.stringify(data);
  }

  // Make the request
  const response = await fetch(url, options);

  // Handle response
  if (!response.ok) {
    let errorMessage;
    try {
      const errorData = await response.json();
      errorMessage = Array.isArray(errorData)
        ? errorData.map((err) => `${err.errorCode}: ${err.message}`).join(', ')
        : errorData.message || 'Unknown error';
    } catch (e) {
      errorMessage = `HTTP error ${response.status}`;
    }

    throw new Error(`Salesforce API error: ${errorMessage}`);
  }

  // Return response data if any
  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json();
  }

  return response.text();
}

/**
 * Convenience methods for common API operations
 */
const SalesforceClient = {
  /**
   * Retrieves the Salesforce instance URL by authenticating with the given environment.
   * @param {Object} env - Environment variables needed for authentication.
   * @returns {Promise<string>} - The Salesforce instance URL.
   */
  instanceUrl: async (env) => {
    const { instanceUrl } = await authenticateWithSalesforce(env);
    log('instanceUrl:', instanceUrl);
    return instanceUrl;
  },

  /**
   * Make a GET request to Salesforce API
   * @param {Object} env - Environment variables
   * @param {string} path - API endpoint path
   * @param {Object} [params] - Query parameters
   * @returns {Promise<Object>} - API response
   */
  get: (env, path, params) => salesforceApiRequest(env, 'GET', path, null, params),

  /**
   * Make a POST request to Salesforce API
   * @param {Object} env - Environment variables
   * @param {string} path - API endpoint path
   * @param {Object} data - Request body
   * @returns {Promise<Object>} - API response
   */
  post: (env, path, data) => salesforceApiRequest(env, 'POST', path, data),

  /**
   * Make a PATCH request to Salesforce API
   * @param {Object} env - Environment variables
   * @param {string} path - API endpoint path
   * @param {Object} data - Request body
   * @returns {Promise<Object>} - API response
   */
  patch: (env, path, data) => salesforceApiRequest(env, 'PATCH', path, data),

  /**
   * Make a DELETE request to Salesforce API
   * @param {Object} env - Environment variables
   * @param {string} path - API endpoint path
   * @returns {Promise<Object>} - API response
   */
  delete: (env, path) => salesforceApiRequest(env, 'DELETE', path),

  /**
   * Query Salesforce using SOQL
   * @param {Object} env - Environment variables
   * @param {string} soql - SOQL query string
   * @returns {Promise<Object>} - Query results
   */
  query: (env, soql) => {
    const encodedQuery = encodeURIComponent(soql);
    return salesforceApiRequest(env, 'GET', `/services/data/${env.SALESFORCE_API_VERSION}/query?q=${encodedQuery}`);
  },

  /**
   * Get all objects from Salesforce API
   * @param {Object} env - Environment variables
   * @param {string} sObject - Salesforce Object Name
   * @returns {Promise<Object>} - API response
   */
  objects: (env) => salesforceApiRequest(env, 'GET', `/services/data/${env.SALESFORCE_API_VERSION}/sobjects`),

  /**
   * Get all objects from Salesforce API
   * @param {Object} env - Environment variables
   * @param {string} sObject - Salesforce Object Name
   * @returns {Promise<Object>} - API response
   */
  describeObject: (env, sObject) =>
    salesforceApiRequest(env, 'GET', `/services/data/${env.SALESFORCE_API_VERSION}/sobjects/${sObject}/describe`),

  /**
   * Create a Deal
   * @param {Object} env - Environment variables
   * @param {Object} data - Deal data - request body
   * @returns {Promise<Object>} - API response
   */
  createDeal: (env, data) => salesforceApiRequest(env, 'POST', `/services/data/${env.SALESFORCE_API_VERSION}/sobjects/Deal__c`, data),

  /**
   * Update a Deal
   * @param {Object} env - Environment variables
   * @param {string} dealId - Deal ID - of deal being updated
   * @param {Object} data - Deal data - request body
   * @returns {Promise<Object>} - API response
   */
  updateDeal: (env, dealId, data) => {
    if (!dealId) throw new Error('dealId is required');
    log('Updating Deal:', dealId, data);
    const path = `/services/data/${env.SALESFORCE_API_VERSION}/sobjects/Deal__c/${dealId}`;
    return salesforceApiRequest(env, 'PATCH', path, data);
  },

  /**
   * Get a Deal Record
   * @param {Object} env - Environment variables
   * @param {string} dealId - Deal ID - of deal being updated
   * @returns {Promise<Object>} - API response
   */
  getDeal: (env, dealId) => {
    const path = `/services/data/${env.SALESFORCE_API_VERSION}/sobjects/Deal__c/${dealId}`;
    return salesforceApiRequest(env, 'GET', path);
  },

  /**
   * Fetch a Deal record by deal number (from Name - Deal-123456).
   * @param {object} env - Environment variables including Salesforce API version.
   * @param {string|number} dealNumber - The deal number identifier (123456).
   * @returns {Promise<object>} Salesforce Deal record.
   */
  getDealByNumber: (env, dealNumber) => {
    if (!dealNumber) throw new Error('dealNumber is required');
    const dealName = `Deal-${dealNumber}`;
    log('Getting Deal by Number:', dealName);
    const path = `/services/data/${env.SALESFORCE_API_VERSION}/sobjects/Deal__c/Name/${dealName}`;
    return salesforceApiRequest(env, 'GET', path);
  },

  /**
   * Create a Note and link it to a record
   * @param {Object} env - Environment variables
   * @param {string} recordId - Salesforce Record ID (to link the note)
   * @param {Object} data - Note data - request body - { title: string, content: string }
   * @returns {Promise<Object>} - API response
   */
  createRecordNote: async (env, recordId, { title, content }) => {
    log('Creating a Record Note:', { recordId, title, content });

    const noteData = {
      Title: title,
      Content: Buffer.from(content).toString('base64'),
    };

    const note = await salesforceApiRequest(env, 'POST', `/services/data/${env.SALESFORCE_API_VERSION}/sobjects/ContentNote`, noteData);
    log('Created Note:', note.id);

    const linkData = {
      ContentDocumentId: note.id,
      LinkedEntityId: recordId,
      ShareType: 'V',
    };

    const link = await salesforceApiRequest(
      env,
      'POST',
      `/services/data/${env.SALESFORCE_API_VERSION}/sobjects/ContentDocumentLink`,
      linkData
    );
    log('Linked Note:', note.id, link.id);

    return { note, link };
  },

  /**
   * Create a Task (Activity Log) linked to a record
   * @param {Object} env - Environment variables
   * @param {string} dealId - Salesforce Record ID (to link the note)
   * @param {Object} content - Note data - request body - { title: string, content: string }
   * @returns {Promise<Object>} - API response
   */
  createDealActivityLog: (env, dealId, content) => {
    log('Creating a Deal Activity Log (Task):', { dealId, content });

    const data = {
      Subject: content,
      Description: 'Via API',
      Status: 'Completed',
      WhatId: dealId,
    };

    const path = `/services/data/${env.SALESFORCE_API_VERSION}/sobjects/Task`;

    return salesforceApiRequest(env, 'POST', path, data);
  },

  /**
   * Upload a file and link it to a record
   * @param {Object} env - Environment variables
   * @param {string} recordId - Salesforce Record ID (to link the note)
   * @param {string} fileName - File name (MyFile.pdf)
   * @param {string} fileContent - Base64 encoded file contents
   * @returns {Promise<Object>} - API response
   */
  uploadLinkedFile: async (env, recordId, fileName, fileContent) => {
    log('Creating a ContentVersion (File) record:', fileName);

    const fileData = {
      PathOnClient: fileName,
      VersionData: fileContent,
    };

    const { id: ContentVersionId } = await salesforceApiRequest(
      env,
      'POST',
      `/services/data/${env.SALESFORCE_API_VERSION}/sobjects/ContentVersion`,
      fileData
    );
    log('Uploaded File - ContentVersion:', ContentVersionId);

    const queryResult = await salesforceApiRequest(
      env,
      'GET',
      `/services/data/${env.SALESFORCE_API_VERSION}/query?q=SELECT ContentDocumentId FROM ContentVersion WHERE Id='${ContentVersionId}'`
    );
    const ContentDocumentId = queryResult.records[0].ContentDocumentId;

    log('ContentDocumentId:', ContentDocumentId);

    const linkData = {
      ContentDocumentId: ContentDocumentId,
      LinkedEntityId: recordId,
      ShareType: 'V',
      Visibility: 'AllUsers',
    };

    const { id: ContentDocumentLink } = await salesforceApiRequest(
      env,
      'POST',
      `/services/data/${env.SALESFORCE_API_VERSION}/sobjects/ContentDocumentLink`,
      linkData
    );
    log('Linked File:', { ContentVersionId, ContentDocumentId, ContentDocumentLink });

    return { ContentVersionId, ContentDocumentId, ContentDocumentLink };
  },

  /**
   * Create an API Update Log linked to a Deal
   * @param {Object} env - Environment variables
   * @param {string} dealId - Salesforce Record ID (to link the note)
   * @param {string} name - Log Title
   * @param {string} message - Log message (full JSON)
   * @returns {Promise<Object>} - API response
   */
  APIUpdateLog: (env, dealId, title, meta, application = {}) => {
    log('Creating a Deal API Update Log:', dealId, title);

    const data = {
      Deal__c: dealId,
      Name: title,
      Metadata__c: meta,
      RawJSON__c: JSON.stringify(application),
    };

    const path = `/services/data/${env.SALESFORCE_API_VERSION}/sobjects/APIUpdateLog__c`;

    return salesforceApiRequest(env, 'POST', path, data);
  },
};

module.exports = {
  salesforceApiRequest,
  SalesforceClient,
};
