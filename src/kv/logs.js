export async function logErrorToKV(env, error, additionalData = {}) {
  const errorId = crypto.randomUUID().replace(/-/g, '').toUpperCase().slice(0, 10);

  const expirationTtl = 7 * 60 * 60 * 24; // 7 days in seconds

  const errorData = {
    id: errorId,
    timestamp: new Date().toISOString(),
    name: error?.name || 'Error',
    message: error?.message || 'Unknown Error',
    stack: error?.stack,
    ...additionalData,
  };

  if (error?.cause) {
    errorData.cause = error.cause;
  }

  console.warn('Error ID:', errorId);
  console.error(error?.message);
  console.warn(error?.stack)

  await env.LOGS.put(`error:${errorId}`, JSON.stringify(errorData), { expirationTtl });

  return { errorId, errorData };
}
