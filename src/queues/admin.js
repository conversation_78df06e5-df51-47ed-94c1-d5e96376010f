import { sendEmailTemplate } from '../postmark';
import { SalesforceClient } from '../salesforce';
import { SalesforceUtils } from '../salesforce/utils';

const log = (...args) => console.log('[ADMIN]', ...args);

export async function adminQueue<PERSON>andler(message, env) {
  log('adminQ<PERSON><PERSON><PERSON><PERSON><PERSON> called');

  try {
    const { application } = message;

    if (!application) {
      console.error('No application data in message');
      return;
    }

    const { uuid, domain, salesforce_id, agent, status, preQualifyFields: prequal } = application;

    log(`Application Status: ${status}`);

    application.salesforceDeal = await (async () => {
      try {
        const instanceUrl = await SalesforceClient.instanceUrl(env);
        const deal = await SalesforceClient.getDeal(env, salesforce_id);
        return {
          name: deal.Name,
          url: `${instanceUrl}/lightning/r/Deal__c/${deal.Id}/view`,
        };
      } catch {
        return null;
      }
    })();

    application.resumeUrl = SalesforceUtils.getResumeUrl(env, domain, status, uuid);

    // TODO: use a switch/case here
    if (['PREQUAL_APPROVED', 'PREQUAL_DENIED'].includes(status)) {
      log(`Processing prequal email to admin for application ${uuid}`);

      const recipientEmails = [env.ADMIN_EMAIL];
      if (agent?.email) recipientEmails.push(agent.email);

      prequal.fundingAmount = `$${prequal.fundingAmount.toLocaleString()}`;
      application.approvalAmount = application.approvalAmount ? `$${application.approvalAmount.toLocaleString()}` : undefined;
      // set denied - used for template subject
      if (status === 'PREQUAL_DENIED') {
        application.denied = true;
      }

      const rawRevenue = prequal.monthlyRevenue;
      const formattedRevenue = rawRevenue
        ? rawRevenue
            .split(/([+-])/)
            .filter(Boolean)
            .map((part) => {
              if (/[+-]/.test(part)) return part; // keep + or -
              return `$${(+part).toLocaleString('en-US')}`;
            })
            .join('')
        : 'N/A';
      prequal.monthlyRevenue = formattedRevenue;
      prequal.annualRevenue =
        typeof prequal.annualRevenue === 'number' && prequal.annualRevenue >= 0
          ? `$${prequal.annualRevenue.toLocaleString('en-US')}`
          : null;

      const email = await sendEmailTemplate(env, {
        to: recipientEmails.join(','),
        template: env.ADMIN_PREQUAL_TEMPLATE,
        placeholders: application,
      });

      log(`Email result: ${JSON.stringify(email)}`);
    } else if (status == 'APP_COMPLETED') {
      log(`Processing completed email to admin for application ${uuid}`);

      const recipientEmails = [env.ADMIN_EMAIL];
      if (agent?.email) recipientEmails.push(agent.email);

      // asign fico from prequal to Owner #1 since we don't collect it on application - used in Postmark template
      application.applicationFields.owners[0].estimatedFICO = prequal.estimatedFICO;
      // remove utm_dur - only used on prequal
      delete application.utm?.utm_dur;

      const email = await sendEmailTemplate(env, {
        to: recipientEmails.join(','),
        template: env.ADMIN_APP_COMPLETED_TEMPLATE,
        placeholders: application,
      });

      log(`Email result: ${JSON.stringify(email)}`);
    } else if (status === 'PREQUAL_FAST_TRACK') {
      console.log('Processing fast track email to admin for application', application.uuid);

      const recipientEmails = [env.ADMIN_EMAIL];
      if (agent?.email) recipientEmails.push(agent.email);

      const email = await sendEmailTemplate(env, {
        to: recipientEmails.join(','),
        template: env.ADMIN_FASTTRACK_TEMPLATE,
        placeholders: application,
      });

      log(`Email result: ${JSON.stringify(email)}`);
    }
  } catch (error) {
    console.error(`Error processing email: ${error.message}`);
    throw error;
  }
}
