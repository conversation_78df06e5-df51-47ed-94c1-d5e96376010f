const log = (...args) => console.log('[QUEUE]', ...args);

import { logErrorToKV } from '../kv/logs';
import { adminQueueHandler } from './admin';
import { emailQueueHandler } from './email';
import { salesforceQueueHandler } from './salesforce';

export async function QueueRouter(batch, env) {
  const IS_TESTING = import.meta.env ? import.meta.env?.MODE === 'test' : false;
  if (IS_TESTING) {
    log('Testing mode, not processing messages');
    return;
  }
  log(`Processing ${batch.messages.length} messages from queue: ${batch.queue}`);

  const messagePromises = batch.messages.map(async (message) => {
    log(`Processing message:`, JSON.stringify(message));
    try {
      // Parse the message body if it's a string
      const data = typeof message.body === 'string' ? JSON.parse(message.body) : message.body;

      let handler;
      if (batch.queue.includes('-email')) {
        handler = emailQueueHandler;
        log(`Processing email queue message`);
      } else if (batch.queue.includes('-salesforce')) {
        handler = salesforceQueueHandler;
        log(`Processing salesforce queue message`);
      } else if (batch.queue.includes('-admin')) {
        handler = adminQueueHandler;
        log(`Processing admin queue message`);
      } else {
        console.warn(`[QUEUE] Unknown queue: ${batch.queue}`);
        message.ack();
        return;
      }

      await handler(data, env);

      // Acknowledge the message was processed successfully
      message.ack();
      log(`Message Acknowledged`);
      return;
    } catch (error) {
      if (error.message === 'SALESFORCE_ID_NOT_FOUND') {
        console.error('SALESFORCE_ID_NOT_FOUND');
        log('SalesforceID not found for existing application');
        log('Attempt', message.attempts, '- retrying in 30s');
        message.retry({ delaySeconds: 30 });
        return;
      }

      const { errorId } = await logErrorToKV(env, error, {
        source: error?.source || 'queueHandler',
        statusCode: error?.statusCode || 500,
      });

      console.warn(`[QUEUE] Error processing message:${errorId} - ${error?.message}`);
      // Retry the message
      // message.retry();
      // not needed to be explicitly called so it goes automatically based on the retry delay
    }
  });

  await Promise.all(messagePromises);
}

export class SendToQueue {
  static async email(env, data, options = {}) {
    return env.EMAIL_QUEUE.send({ type: 'email', ...data }, options);
  }

  static async salseforce(env, data, options = {}) {
    return env.SALESFORCE_QUEUE.send({ type: 'salesforce', ...data }, options);
  }

  static async admin(env, data, options = {}) {
    return env.ADMIN_QUEUE.send({ type: 'admin', ...data }, options);
  }
}
