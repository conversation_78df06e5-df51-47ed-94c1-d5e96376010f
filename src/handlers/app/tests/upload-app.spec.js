import { SELF, env } from 'cloudflare:test';
import { describe, expect, it, vi, beforeEach, beforeAll, afterAll } from 'vitest';
import { ApplicationD1 } from '../../../db/applications.js';
import { SendToQueue } from '../../../queues/index.js';
import { generateCreateAppRequest } from '../../../../test/testUtils.js';

// valid pdf data uri

const BASE_URL = 'http://localhost:8787';

const generateDocuments = (count = 1) => {
  const documents = [];
  for (let i = 0; i < count; i++) {
    documents.push({
      name: `document${i + 1}.pdf`,
      type: 'application/pdf',
      size: 1024 * 100, // 100KB
      dataUrl: `data:application/pdf;base64,JVBERi0xLjUKJeLjz9MKMSAwIG9iago8PC9UeXBlIC9DYXRhbG9nL1BhZ2VzIDIgMCBSCj4+CmVuZG9iagoyIDAgb2JqCjw8L1R5cGUgL1BhZ2VzL0tpZHMgWzMgMCBSXS9Db3VudCAxCj4+CmVuZG9iagozIDAgb2JqCjw8L1R5cGUgL1BhZ2UvUGFyZW50IDIgMCBSL01lZGlhQm94IFswIDAgNjEyIDc5Ml0KPj4KZW5kb2JqCnhyZWYKMCA0CjAwMDAwMDAwMDAgNjU1MzUgZiAKMDAwMDAwMDExNiAwMDAwMCBuIAowMDAwMDAwMDY4IDAwMDAwIG4gCjAwMDAwMDAxNDUgMDAwMDAgbiAKdHJhaWxlcgo8PC9Sb290IDEgMCBSCj4+CnN0YXJ0eHJlZgowCjUlRU9G`,
      preview: 'https://example.com/preview.jpg',
      lastModified: Date.now(),
    });
  }
  return documents;
};

describe('Upload App Handler', () => {
  let testAppUuid;
  const ogSalesforce = SendToQueue.salseforce;

  beforeAll(async () => {
    SendToQueue.salseforce = vi.fn();

    // Create a test application and move it to APP_DOCS_PENDING status
    const requestData = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });
    const createData = await createResponse.json();
    testAppUuid = createData.uuid;

    // Update application to APP_DOCS_PENDING status and add applicationFields
    await ApplicationD1.update(env, testAppUuid, {
      status: 'APP_DOCS_PENDING',
      applicationFields: {
        businessName: 'Test Business LLC',
        owners: [{ firstName: 'Test', lastName: 'User' }],
      },
    });
  });

  afterAll(async () => {
    SendToQueue.salseforce = ogSalesforce;
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('uploads documents and sets status to APP_COMPLETED', async () => {
    const bankStatements = generateDocuments(3);

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/upload`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ bankStatements }),
    });

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data.data).toMatchObject({
      uuid: testAppUuid,
      status: 'APP_COMPLETED',
    });

    expect(SendToQueue.salseforce).toHaveBeenCalled();
  });

  it('rejects upload when application status is not APP_DOCS_PENDING', async () => {
    // Create another app with different status
    const requestData = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });
    const createData = await createResponse.json();
    const wrongStatusUuid = createData.uuid;

    const bankStatements = generateDocuments(1);

    const response = await SELF.fetch(`${BASE_URL}/app/${wrongStatusUuid}/upload`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ bankStatements }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toMatchInlineSnapshot(`"Validation Error: Invalid documents"`);
  });

  it('validates document schema - requires at least 3 documents', async () => {
    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/upload`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ bankStatements: generateDocuments(2) }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toBe('Validation Error: Invalid documents');
  });

  it('validates document schema - rejects non-PDF files', async () => {
    const invalidDocuments = [
      {
        name: 'document.txt',
        type: 'text/plain',
        size: 1024,
        dataUrl: 'data:text/plain;base64,dGVzdCBiYXNlNjQgY29udGVudA==',
        preview: 'https://example.com/preview.jpg',
        lastModified: Date.now(),
      },
      {
        name: 'document2.txt',
        type: 'text/plain',
        size: 1024,
        dataUrl: 'data:text/plain;base64,dGVzdCBiYXNlNjQgY29udGVudA==',
        preview: 'https://example.com/preview.jpg',
        lastModified: Date.now(),
      },
      {
        name: 'document3.pdf',
        type: 'application/pdf',
        size: 1024,
        dataUrl: 'data:text/plain;base64,dGVzdCBiYXNlNjQgY29udGVudA==',
        preview: 'https://example.com/preview.jpg',
        lastModified: Date.now(),
      },
    ];

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/upload`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ bankStatements: invalidDocuments }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toBe('Validation Error: Invalid documents');
  });

  it('validates document schema - rejects more than 6 documents', async () => {
    const tooManyDocuments = generateDocuments(7);

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/upload`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ bankStatements: tooManyDocuments }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toBe('Validation Error: Invalid documents');
  });

  it('handles queue errors gracefully but still uploads documents', async () => {
    vi.spyOn(SendToQueue, 'salseforce').mockRejectedValueOnce(new Error('Queue failed'));

    const bankStatements = generateDocuments(3);

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/upload`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ bankStatements }),
    });

    expect(response.status).toBe(500);

    const data = await response.json();
    expect(data.error).toBe('Internal Server Error');
  });

  it('returns 404 for non-existent application', async () => {
    const bankStatements = generateDocuments(3);

    const response = await SELF.fetch(`${BASE_URL}/app/non-existent-uuid/upload`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ bankStatements }),
    });

    expect(response.status).toBe(404);

    const data = await response.json();
    expect(data.error).toBe('Application "non-existent-uuid" not found');
  });
});
