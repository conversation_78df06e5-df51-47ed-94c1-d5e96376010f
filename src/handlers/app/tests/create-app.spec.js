import { SELF, env } from 'cloudflare:test';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import { ApplicationD1 } from '../../../db/applications.js';
import { SendToQueue } from '../../../queues/index.js';
import { generateCreateAppRequest, generateDeniedPrequalData, generatePrequalData } from '../../../../test/testUtils.js';

const BASE_URL = 'http://localhost:8787';

describe('Create App Handler', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('creates approved application with agent assignment and queues messages', async () => {
    const requestData = generateCreateAppRequest();

    const response = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });

    expect(response.status).toBe(201);

    const data = await response.json();
    expect(data).toMatchObject({
      uuid: expect.any(String),
      status: 'PREQUAL_APPROVED',
      agent: expect.objectContaining({
        name: expect.any(String),
        email: expect.any(String),
      }),
      approvalAmount: 75000,
    });

    const savedApp = await ApplicationD1.get(env, data.uuid);
    expect(savedApp).toMatchObject({
      uuid: data.uuid,
      status: 'PREQUAL_APPROVED',
      version: env.VERSION,
      preQualifyFields: requestData.preQualifyFields,
      domain: requestData.domain,
      agent: expect.objectContaining({
        name: expect.any(String),
        email: expect.any(String),
      }),
      approvalAmount: 75000,
      reason: null,
      created_at: expect.any(String),
      updated_at: expect.any(String),
      meta: expect.any(Object),
    });
  });

  it('creates denied application with reason and no agent assignment', async () => {
    const requestData = generateCreateAppRequest({
      preQualifyFields: generateDeniedPrequalData(),
    });

    const response = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });

    expect(response.status).toBe(201);

    const data = await response.json();
    expect(data).toMatchObject({
      uuid: expect.any(String),
      status: 'PREQUAL_DENIED',
      reason: expect.any(String),
    });

    const savedApp = await ApplicationD1.get(env, data.uuid);
    expect(savedApp.uuid).toBe(data.uuid);
    expect(savedApp.status).toBe('PREQUAL_DENIED');
    expect(savedApp.reason).toBe(data.reason);
    expect(savedApp.agent).toBeNull();
  });

  it('assigns pre-assigned agent when utm_rep is provided', async () => {
    const requestData = generateCreateAppRequest({
      utm: { utm_rep: 'dev' },
    });

    const response = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });

    expect(response.status).toBe(201);

    const data = await response.json();
    expect(data.agent.name).toMatchInlineSnapshot(`"Pre-Assigned Agent #1"`);

    const savedApp = await ApplicationD1.get(env, data.uuid);
    expect(savedApp.agent.name).toMatchInlineSnapshot(`"Pre-Assigned Agent #1"`);
  });

  it('trim prequal fields and utm fields ', async () => {
    const requestData = generateCreateAppRequest({
      preQualifyFields: generatePrequalData({
        businessName: '  Test Business LLC  ',
        firstName: '  Test  ',
        lastName: '  User  ',
      }),
      utm: {
        utm_source: 'google',
        utm_campaign: 'test-campaign',
        utm_medium: 'cpc',
        utm_rep: 'test-rep',
        invalidField: 123,
        longString: 'x'.repeat(300),
      },
    });

    const response = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });

    expect(response.status).toBe(201);

    const data = await response.json();
    const savedApp = await ApplicationD1.get(env, data.uuid);

    expect(savedApp.preQualifyFields.businessName).toBe('Test Business LLC');
    expect(savedApp.preQualifyFields.firstName).toBe('Test');
    expect(savedApp.preQualifyFields.lastName).toBe('User');
    expect(savedApp.preQualifyFields.email).toBe('<EMAIL>');
    expect(savedApp.utm).toEqual({
      utm_source: 'google',
      utm_campaign: 'test-campaign',
      utm_medium: 'cpc',
      utm_rep: 'test-rep',
      longString: 'x'.repeat(255),
    });
  });

  it('validates required fields and returns validation error', async () => {
    const invalidData = {
      preQualifyFields: {
        fundingAmount: 1000,
        businessName: 'T',
        email: 'invalid-email',
        consent: false,
      },
      domain: 'app.pinnaclefunding.com',
    };

    const response = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(invalidData),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('validates domain enum and rejects invalid domains', async () => {
    const requestData = generateCreateAppRequest({
      domain: 'invalid-domain.com',
    });

    const response = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('handles missing preQualifyFields', async () => {
    const invalidData = {
      domain: 'app.pinnaclefunding.com',
    };

    const response = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(invalidData),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('handles database errors gracefully', async () => {
    vi.spyOn(ApplicationD1, 'create').mockRejectedValueOnce(new Error('Database connection failed'));

    const requestData = generateCreateAppRequest();

    const response = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });

    expect(response.status).toBe(500);

    const data = await response.json();
    expect(data.error).toBe('Internal Server Error');
    expect(data.errorId).toBeDefined();
  });

  it('handles queue errors gracefully but still creates application', async () => {
    vi.spyOn(SendToQueue, 'salseforce').mockRejectedValueOnce(new Error('Queue failed'));

    const requestData = generateCreateAppRequest();

    const response = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });

    expect(response.status).toBe(500);

    const data = await response.json();
    expect(data.error).toBe('Internal Server Error');
  });

  describe('Duplicate Prevention (24-hour window)', () => {
    it('should prevent creating duplicate application within 24 hours for same email', async () => {
      const email = '<EMAIL>';
      const requestData = generateCreateAppRequest({
        preQualifyFields: generatePrequalData({ email }),
      });

      // Create first application
      const firstResponse = await SELF.fetch(`${BASE_URL}/app`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });

      expect(firstResponse.status).toBe(201);
      const firstData = await firstResponse.json();

      // Try to create second application with same email immediately
      const secondResponse = await SELF.fetch(`${BASE_URL}/app`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });

      expect(secondResponse.status).toBe(201);
      const secondData = await secondResponse.json();

      // Should return existing application with duplicate flag
      expect(secondData.duplicate).toBe(true);
      expect(secondData.uuid).toBe(firstData.uuid);
      expect(secondData.status).toBe(firstData.status);
    });

    it('should allow creating new application after 24 hours for same email', async () => {
      const email = '<EMAIL>';
      const requestData = generateCreateAppRequest({
        preQualifyFields: generatePrequalData({ email }),
      });

      // Create application with timestamp 25 hours ago
      const oldTimestamp = new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString();
      const oldApplication = {
        uuid: crypto.randomUUID(),
        version: env.VERSION,
        status: 'PREQUAL_APPROVED',
        created_at: oldTimestamp,
        updated_at: oldTimestamp,
        preQualifyFields: generatePrequalData({ email }),
        agent: null,
        utm: {},
        domain: 'app.pinnaclefunding.com',
        meta: { initiated: { timestamp: oldTimestamp } },
        approvalAmount: 75000,
      };

      await ApplicationD1.create(env, oldApplication);

      // Try to create new application with same email
      const response = await SELF.fetch(`${BASE_URL}/app`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });

      expect(response.status).toBe(201);
      const data = await response.json();

      // Should create new application (no duplicate flag)
      expect(data.duplicate).toBeUndefined();
      expect(data.uuid).not.toBe(oldApplication.uuid);
    });

    it('should handle case-insensitive email matching for duplicates', async () => {
      const baseEmail = '<EMAIL>';
      const lowerEmail = baseEmail.toLowerCase();

      // Create first application with uppercase email
      const firstRequest = generateCreateAppRequest({
        preQualifyFields: generatePrequalData({ email: baseEmail }),
      });

      const firstResponse = await SELF.fetch(`${BASE_URL}/app`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(firstRequest),
      });

      expect(firstResponse.status).toBe(201);
      const firstData = await firstResponse.json();

      // Try to create second application with lowercase email
      const secondRequest = generateCreateAppRequest({
        preQualifyFields: generatePrequalData({ email: lowerEmail }),
      });

      const secondResponse = await SELF.fetch(`${BASE_URL}/app`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(secondRequest),
      });

      expect(secondResponse.status).toBe(201);
      const secondData = await secondResponse.json();

      // Should return existing application with duplicate flag
      expect(secondData.duplicate).toBe(true);
      expect(secondData.uuid).toBe(firstData.uuid);
    });

    it('should allow different emails to create separate applications', async () => {
      const email1 = '<EMAIL>';
      const email2 = '<EMAIL>';

      const request1 = generateCreateAppRequest({
        preQualifyFields: generatePrequalData({ email: email1 }),
      });

      const request2 = generateCreateAppRequest({
        preQualifyFields: generatePrequalData({ email: email2 }),
      });

      // Create first application
      const response1 = await SELF.fetch(`${BASE_URL}/app`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request1),
      });

      expect(response1.status).toBe(201);
      const data1 = await response1.json();

      // Create second application with different email
      const response2 = await SELF.fetch(`${BASE_URL}/app`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request2),
      });

      expect(response2.status).toBe(201);
      const data2 = await response2.json();

      // Should create separate applications
      expect(data1.duplicate).toBeUndefined();
      expect(data2.duplicate).toBeUndefined();
      expect(data1.uuid).not.toBe(data2.uuid);
    });
  });
});
