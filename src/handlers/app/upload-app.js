import { zValidator } from '@hono/zod-validator';
import { createFactory } from 'hono/factory';
import { ApplicationD1 } from '../../db/applications';
import { putAppBankStatements } from '../../kv';
import { SendToQueue } from '../../queues';
import { uploadDocumentsSchema } from '../../schema/application';
import { getMeta } from '../../utils';
import { AppError, cleanedApplication } from '../../utils/helpers';
import { requireApplication } from '../../middlewares/require-application';

const factory = createFactory();

const validator = zValidator('json', uploadDocumentsSchema, async (result) => {
  if (!result.success) {
    throw new AppError('Validation Error: Invalid documents', 400, 'validationError', result.error);
  }
});

export const uploadAppHandlers = factory.createHandlers(validator, requireApplication, async (c) => {
  const uuid = c.req.param('uuid');
  const timestamp = c.get('timestamp');
  let application = c.get('application');
  const bankStatements = c.req.valid('json').bankStatements ?? [];
  const bank_stmts = bankStatements.length;

  console.log('Uploaded', bank_stmts, 'documents');

  // Verify the application is in a state that allows document upload
  if (application.status !== 'APP_DOCS_PENDING') {
    console.error(`Application status ${application.status} doesn't allow document upload`);
    throw new AppError(`Documents can't be uploaded`, 400, 'uploadApp', `Invalid application status: ${application.status}`);
  }

  const columnsToUpdate = {
    completed_at: timestamp,
    status: 'APP_COMPLETED',
    meta: { ...application.meta, uploaded: getMeta(c.req.raw, timestamp) },
    bank_stmts,
  };

  application = { ...application, ...columnsToUpdate };

  const promises = [
    ApplicationD1.update(c.env, uuid, columnsToUpdate),
    putAppBankStatements(c.env, uuid, bankStatements),
    SendToQueue.salseforce(c.env, { application, hasBankStatements: true }),
  ];

  await Promise.all(promises);

  return c.json({ data: cleanedApplication(application) });
});
