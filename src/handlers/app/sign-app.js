import { createFactory } from 'hono/factory';
import { ApplicationD1 } from '../../db/applications';
import { SendToQueue } from '../../queues';
import { getMeta } from '../../utils';
import { AppError, cleanedApplication } from '../../utils/helpers';
import { requireApplication } from '../../middlewares/require-application';
import { isPandaDocSigned } from './pandadoc-status';

const factory = createFactory();

export const signAppHandlers = factory.createHandlers(requireApplication, async (c) => {
  const uuid = c.req.param('uuid');
  const timestamp = c.get('timestamp');
  let application = c.get('application');
  const { status } = application;

  if (status !== 'APP_SUBMITTED') {
    if (['APP_SIGNED', 'APP_DOCS_PENDING', 'APP_COMPLETED'].includes(status)) {
      console.warn(`Application ${uuid} is already signed`);
      return c.json({ data: cleanedApplication(application) });
    }
    console.error(`Application status ${status} can't be signed`);
    throw new AppError(`Application can't be signed`, 400, 'signApp', `Status is ${status}`);
  }

  const isSigned = await isPandaDocSigned(c.env, application.pandadoc?.document?.id);

  const IS_TESTING = import.meta.env ? import.meta.env?.MODE === 'test' : false;

  if (!IS_TESTING && !isSigned) {
    throw new AppError('Document is not yet signed', 400, 'signApp');
  }

  const columnsToUpdate = {
    signed_at: timestamp,
    status: 'APP_SIGNED',
    meta: {
      ...application.meta,
      signed: getMeta(c.req.raw, timestamp),
    },
  };

  application = { ...application, ...columnsToUpdate };

  await Promise.all([
    ApplicationD1.update(c.env, uuid, columnsToUpdate),
    SendToQueue.email(c.env, { application }),
    SendToQueue.salseforce(c.env, { application }),
  ]);

  return c.json({ data: cleanedApplication(application) });
});
