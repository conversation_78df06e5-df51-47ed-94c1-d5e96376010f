import { zValidator } from '@hono/zod-validator';
import { createFactory } from 'hono/factory';
import { ApplicationD1 } from '../../db/applications';
import { putAppBankStatements } from '../../kv';
import { SendToQueue } from '../../queues';
import { bankStatementsSchema } from '../../schema/application';
import { getMeta } from '../../utils';
import { AppError, cleanedApplication, extractAndSanitizePIIFields } from '../../utils/helpers';
import { requireApplication } from '../../middlewares/require-application';

const factory = createFactory();

const validator = zValidator('json', bankStatementsSchema, async (result) => {
  if (!result.success) {
    throw new AppError('Validation Error: Invalid applicationFields', 400, 'validationError', result.error);
  }
});

export const completeAppHandlers = factory.createHandlers(validator, requireApplication, async (c) => {
  const uuid = c.req.param('uuid');
  const timestamp = c.get('timestamp');
  let application = c.get('application');
  const bankStatements = c.req.valid('json').bankStatements ?? [];
  const bank_stmts = bankStatements.length;
  const hasBankStatements = bank_stmts > 0;

  console.log('Uploaded', bank_stmts, 'bank statements');

  // Verify the application is in a state that can be completed
  if (application.status !== 'APP_SIGNED') {
    console.error(`Application status ${application.status} can't be completed`);
    throw new AppError(`Application can't be completed`, 400, 'completeApp', `Status is ${application.status}`);
  }

  const { sanitizedApplicationFields } = extractAndSanitizePIIFields(application.applicationFields);

  const columnsToUpdate = {
    applicationFields: sanitizedApplicationFields,
    completed_at: timestamp,
    status: hasBankStatements ? 'APP_COMPLETED' : 'APP_DOCS_PENDING',
    meta: { ...application.meta, completed: getMeta(c.req.raw, timestamp) },
    bank_stmts,
  };

  application = { ...application, ...columnsToUpdate };

  const promises = [
    ApplicationD1.update(c.env, uuid, columnsToUpdate),
    SendToQueue.admin(c.env, { application }),
    SendToQueue.salseforce(c.env, { application, hasBankStatements }),
  ];
  if (hasBankStatements) promises.push(putAppBankStatements(c.env, uuid, bankStatements));

  await Promise.all(promises);

  console.log('App Status:', application.status);

  return c.json({ data: cleanedApplication(application) });
});
