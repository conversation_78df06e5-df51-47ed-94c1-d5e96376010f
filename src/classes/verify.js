import { z } from 'zod';

const log = (...args) => console.log('[Verify]', ...args);

export class Verify {
  static _parseEmail(email) {
    return z.string().email().parse(email);
  }

  static _parsePhone(phone) {
    return z
      .string()
      .regex(/^\+?[1-9]\d{1,14}$/)
      .parse(phone);
  }

  static async email(env, email) {
    if (!env) throw new Error('Missing env on Verify.email');
    this._parseEmail(email); // throws if invalid

    // Check if email exists in DB
    const cached = await env.DB.prepare('SELECT result, created_at FROM email_verifications WHERE email = ?').bind(email).first();

    if (cached) {
      const daysOld = (Date.now() - new Date(cached.created_at)) / 86400000;
      if (daysOld <= 30) {
        log('Email cached in DB', email, cached.created_at);
        const { score, disposable, state } = JSON.parse(cached.result);
        return { score, disposable, state };
      }
      // else: skip cached, fetch fresh
    }

    // Call Emailable API
    const apiKey = env.EMAILABLE_API_KEY;
    if (!apiKey) throw new Error('Missing EMAILABLE_API_KEY in env');

    const url = `https://api.emailable.com/v1/verify?email=${encodeURIComponent(email)}&api_key=${encodeURIComponent(apiKey)}`;
    const res = await fetch(url);

    if (!res.ok) {
      log('Emailable API error', res.status, res.statusText);
      throw new Error(`Emailable API error: ${res.status} ${res.statusText}`);
    }

    const apiResult = await res.json();
    const { score, disposable, state } = apiResult;

    await env.DB.prepare('INSERT OR REPLACE INTO email_verifications (email, result, created_at) VALUES (?, ?, CURRENT_TIMESTAMP)')
      .bind(email, JSON.stringify(apiResult))
      .run();

    log(`Saved Email: ${email} to DB, ${score}%, ${state}${disposable ? ' [DISPOSABLE]' : ''}`);

    return { score, disposable, state };
  }

  static async phone(env, phone) {
    if (!env) throw new Error('Missing env on Verify.phone');
    this._parsePhone(phone); // throws if invalid

    const cached = await env.DB.prepare('SELECT result, created_at FROM phone_verifications WHERE phone = ?').bind(phone).first();

    if (cached) {
      const daysOld = (Date.now() - new Date(cached.created_at)) / 86400000;
      if (daysOld <= 30) {
        log('Phone cached in DB', phone, cached.created_at);
        const apiResult = JSON.parse(cached.result);
        const { valid } = apiResult;
        const { carrier_name = null, type = null } = apiResult.line_type_intelligence ?? {};
        return { valid, carrier_name, type };
      }
      // else: skip cached, fetch fresh
    }

    const { TWILIO_API_KEY_SID, TWILIO_API_KEY_SECRET } = env;
    if (!TWILIO_API_KEY_SID || !TWILIO_API_KEY_SECRET) {
      throw new Error('Missing Twilio credentials in env');
    }

    const res = await fetch(`https://lookups.twilio.com/v2/PhoneNumbers/${phone}?Fields=line_type_intelligence`, {
      headers: {
        Authorization: `Basic ${Buffer.from(`${TWILIO_API_KEY_SID}:${TWILIO_API_KEY_SECRET}`).toString('base64')}`,
      },
    });

    if (!res.ok) {
      log('Twilio API error', res.status, res.statusText);
      throw new Error(`Twilio API error: ${res.status} ${res.statusText}`);
    }

    const apiResult = await res.json();

    const { valid } = apiResult;
    const { carrier_name = null, type = null } = apiResult.line_type_intelligence ?? {};

    await env.DB.prepare('INSERT OR REPLACE INTO phone_verifications (phone, result, created_at) VALUES (?, ?, CURRENT_TIMESTAMP)')
      .bind(phone, JSON.stringify(apiResult))
      .run();

    log(`Saved phone: ${phone} to DB, ${valid ? 'VALID' : 'INVALID'}, ${carrier_name}, ${type}`);

    return { valid, carrier_name, type };
  }
}
