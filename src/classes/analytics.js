export class AnalyticsD1 {
  static async GoogleAdwords(env) {
    if (!env) throw new Error('Missing env on AnalyticsD1.GoogleAdwords');
    const { results } = await env.DB.prepare('SELECT * FROM view_google_adwords').all();
    return results;
  }

  static async TopKeywords(env) {
    if (!env) throw new Error('Missing env on AnalyticsD1.TopKeywords');
    const { results } = await env.DB.prepare('SELECT * FROM view_utm_term').all();
    return results;
  }

  static async WeeklyApps(env) {
    if (!env) throw new Error('Missing env on AnalyticsD1.WeeklyApps');
    const { results } = await env.DB.prepare('SELECT * FROM view_apps_by_week').all();
    return results;
  }
}
